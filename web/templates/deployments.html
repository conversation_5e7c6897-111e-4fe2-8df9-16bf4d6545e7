{{template "base.html" .}}

{{define "head"}}
<link href="/static/css/dashboard.css" rel="stylesheet">
{{end}}

{{define "content"}}
<!-- 页面标题和操作按钮 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1><i class="bi bi-play-circle me-2"></i>部署管理</h1>
            <p class="subtitle">管理蜜罐服务部署</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createDeploymentModal">
                <i class="bi bi-plus-circle me-2"></i>创建部署
            </button>
            <button class="btn btn-outline-secondary" onclick="refreshDeployments()">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-left-primary h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总部署数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-deployments">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-play-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card success h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            运行中
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-deployments">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-check-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            已停止
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="stopped-deployments">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-pause-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card danger h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            失败
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="failed-deployments">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-x-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-lg-3 col-md-6 mb-3 mb-lg-0">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="搜索部署名称...">
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="status-filter">
                    <option value="">所有状态</option>
                    <option value="running">运行中</option>
                    <option value="stopped">已停止</option>
                    <option value="failed">失败</option>
                    <option value="pending">等待中</option>
                </select>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="node-filter">
                    <option value="">所有节点</option>
                    <!-- 节点选项将通过JavaScript动态加载 -->
                </select>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="template-filter">
                    <option value="">所有模板</option>
                    <!-- 模板选项将通过JavaScript动态加载 -->
                </select>
            </div>
            <div class="col-lg-3 text-end">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-success btn-sm" onclick="batchStart()" title="批量启动">
                        <i class="bi bi-play me-1"></i>启动
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="batchStop()" title="批量停止">
                        <i class="bi bi-pause me-1"></i>停止
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="batchDelete()" title="批量删除">
                        <i class="bi bi-trash me-1"></i>删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 部署列表 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">部署列表</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportDeployments()">
                        <i class="bi bi-download me-2"></i>导出数据
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="refreshAll()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新所有状态
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table" id="deployments-table">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        <th>名称</th>
                        <th>节点</th>
                        <th>模板</th>
                        <th>状态</th>
                        <th>容器ID</th>
                        <th>创建时间</th>
                        <th>最后更新</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="loading">
                                <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="部署分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 创建部署模态框 -->
<div class="modal fade" id="createDeploymentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>创建部署
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="create-deployment-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deployment-name" class="form-label">部署名称</label>
                                <input type="text" class="form-control" id="deployment-name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="deployment-node" class="form-label">目标节点</label>
                                <select class="form-select" id="deployment-node" required>
                                    <option value="">选择节点</option>
                                    <!-- 节点选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="deployment-template" class="form-label">模板</label>
                        <select class="form-select" id="deployment-template" required>
                            <option value="">选择模板</option>
                            <!-- 模板选项将通过JavaScript动态加载 -->
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="deployment-ports" class="form-label">端口映射</label>
                        <input type="text" class="form-control" id="deployment-ports"
                               placeholder="例如: 22:2222,80:8080">
                        <div class="form-text">格式: 容器端口:主机端口，多个端口用逗号分隔</div>
                    </div>

                    <div class="mb-3">
                        <label for="deployment-env" class="form-label">环境变量</label>
                        <textarea class="form-control" id="deployment-env" rows="3"
                                  placeholder='{"ENV_VAR": "value"}'></textarea>
                        <div class="form-text">JSON格式的环境变量</div>
                    </div>

                    <div class="mb-3">
                        <label for="deployment-volumes" class="form-label">数据卷</label>
                        <input type="text" class="form-control" id="deployment-volumes"
                               placeholder="例如: /host/path:/container/path">
                        <div class="form-text">格式: 主机路径:容器路径，多个卷用逗号分隔</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createDeployment()">创建部署</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script src="/static/js/deployments.js"></script>
{{end}}
