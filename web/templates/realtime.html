{{template "base.html" .}}

{{define "head"}}
<link href="/static/css/dashboard.css" rel="stylesheet">
<style>
.realtime-log {
    background: #1a1a1a;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    height: 400px;
    overflow-y: auto;
    padding: 1rem;
    border-radius: 0.5rem;
}

.realtime-log .log-entry {
    margin-bottom: 0.5rem;
    padding: 0.25rem;
    border-left: 3px solid transparent;
}

.realtime-log .log-entry.info {
    border-left-color: #17a2b8;
}

.realtime-log .log-entry.warning {
    border-left-color: #ffc107;
    color: #ffc107;
}

.realtime-log .log-entry.error {
    border-left-color: #dc3545;
    color: #dc3545;
}

.realtime-log .log-entry.success {
    border-left-color: #28a745;
    color: #28a745;
}

.connection-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #dc3545;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: #28a745;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
</style>
{{end}}

{{define "content"}}
<!-- 页面标题和操作按钮 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1><i class="bi bi-eye me-2"></i>实时监控</h1>
            <p class="subtitle">实时查看系统状态和攻击活动</p>
        </div>
        <div class="d-flex gap-2">
            <div class="connection-status">
                <span class="status-indicator" id="ws-status"></span>
                <span id="ws-status-text">连接中...</span>
            </div>
            <button class="btn btn-outline-secondary" onclick="clearLogs()">
                <i class="bi bi-trash me-2"></i>清空日志
            </button>
        </div>
    </div>
</div>

<!-- 实时统计 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-left-primary h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            在线连接
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-connections">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-wifi text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            实时攻击
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="realtime-attacks">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-shield-exclamation text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card success h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            运行节点
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-nodes">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-hdd-network text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card info h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            活跃部署
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-deployments">0</div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-play-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时图表 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-activity me-2"></i>实时活动图表
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="realtime-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-geo-alt me-2"></i>攻击来源地图
                </h6>
            </div>
            <div class="card-body">
                <div id="attack-map" style="height: 300px;">
                    <!-- 地图将通过JavaScript加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 实时日志和最新攻击 -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="bi bi-terminal me-2"></i>实时日志
                    </h6>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="auto-scroll" checked>
                        <label class="form-check-label" for="auto-scroll">自动滚动</label>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="realtime-log" id="realtime-log">
                    <div class="log-entry info">
                        <span class="text-muted">[系统]</span> 实时监控已启动，等待连接...
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-exclamation-triangle me-2"></i>最新攻击
                </h6>
            </div>
            <div class="card-body">
                <div id="recent-attacks">
                    <div class="text-center text-muted">
                        <i class="bi bi-shield-check" style="font-size: 2rem;"></i>
                        <p class="mt-2">暂无攻击记录</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 攻击详情模态框 -->
<div class="modal fade" id="attackDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-info-circle me-2"></i>攻击详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="attack-detail-content">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" onclick="blockAttackerIP()">封禁IP</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script src="/static/js/realtime.js"></script>
{{end}}
