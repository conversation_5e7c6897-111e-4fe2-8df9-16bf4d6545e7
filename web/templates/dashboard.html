{{template "base.html" .}}

{{define "head"}}
<link href="/static/css/dashboard.css" rel="stylesheet">
{{end}}

{{define "content"}}
<!-- 页面标题和操作按钮 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1><i class="bi bi-speedometer2 me-2"></i>系统仪表板</h1>
            <p class="subtitle">蜜罐系统运行状态概览</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-secondary" onclick="refreshDashboard()">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-left-primary h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            在线节点
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-nodes">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-hdd-network text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card success h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃部署
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-deployments">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-play-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            今日攻击
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-attacks">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-shield-exclamation text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card info h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            可用模板
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-templates">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-layers text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-graph-up me-2"></i>系统概览
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="overview-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-pie-chart me-2"></i>节点状态分布
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie">
                    <canvas id="node-status-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据表格和系统状态 -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-clock-history me-2"></i>最近活动
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="recent-activities-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>事件</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="recent-activities">
                            <tr>
                                <td colspan="3" class="text-center">
                                    <div class="loading">
                                        <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-cpu me-2"></i>系统状态
                </h6>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-6">
                        <div class="text-center">
                            <h6 class="text-sm font-weight-bold">CPU使用率</h6>
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%" id="cpu-usage">0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h6 class="text-sm font-weight-bold">内存使用率</h6>
                            <div class="progress">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 0%" id="memory-usage">0%</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h6 class="text-sm font-weight-bold mb-3">服务状态</h6>
                    <div id="service-status">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>管理平台</span>
                            <span class="badge badge-success">运行中</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>DecoyWatch</span>
                            <span class="badge badge-secondary" id="decoywatch-status">检查中...</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>WebSocket</span>
                            <span class="badge badge-info" id="websocket-status">连接中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-lightning me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="/nodes" class="btn btn-outline-primary w-100">
                            <i class="bi bi-hdd-network me-2"></i>管理节点
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="/templates" class="btn btn-outline-success w-100">
                            <i class="bi bi-layers me-2"></i>管理模板
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="/deployments" class="btn btn-outline-warning w-100">
                            <i class="bi bi-play-circle me-2"></i>管理部署
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="/intelligence" class="btn btn-outline-danger w-100">
                            <i class="bi bi-shield-exclamation me-2"></i>查看情报
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script src="/static/js/dashboard.js"></script>
{{end}}
