<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 蜜罐管理平台</title>

    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <!-- 主样式 -->
    <link href="/static/css/main.css" rel="stylesheet">
    <!-- 登录页面样式 -->
    <link href="/static/css/login.css" rel="stylesheet">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-header">
            <div class="icon">
                <i class="bi bi-shield-check"></i>
            </div>
            <h2>蜜罐管理平台</h2>
            <p class="mb-0">Honeypot Management System</p>
        </div>

        <div class="login-form">
            <!-- 消息提示 -->
            <div id="alert-container">
                {{if .ErrorMessage}}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>{{.ErrorMessage}}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {{end}}

                {{if .InfoMessage}}
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-info-circle me-2"></i>{{.InfoMessage}}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {{end}}
            </div>

            <!-- 登录表单 -->
            <form id="loginForm" method="POST" action="/api/v1/auth/login">
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required autocomplete="username">
                    <label for="username">
                        <i class="bi bi-person me-2"></i>用户名
                    </label>
                </div>

                <div class="form-floating">
                    <input type="password" class="form-control" id="password" name="password" placeholder="密码" required autocomplete="current-password">
                    <label for="password">
                        <i class="bi bi-lock me-2"></i>密码
                    </label>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        记住我
                    </label>
                </div>

                <button type="submit" class="btn btn-primary btn-login">
                    <i class="bi bi-box-arrow-in-right me-2"></i>登录
                </button>
            </form>

            <div class="forgot-password">
                <a href="/forgot-password">忘记密码？</a>
            </div>
        </div>

        <div class="system-info">
            <div class="row">
                <div class="col-6">
                    <i class="bi bi-server me-1"></i>系统状态: <span class="text-success">正常</span>
                </div>
                <div class="col-6">
                    <i class="bi bi-clock me-1"></i>{{if .CurrentTime}}{{.CurrentTime}}{{else}}{{now.Format "15:04"}}{{end}}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- 公共JavaScript -->
    <script src="/static/js/main.js"></script>
    <!-- 登录页面JavaScript -->
    <script src="/static/js/login.js"></script>
</body>
</html>
