{{template "base.html" .}}

{{define "head"}}
<link href="/static/css/dashboard.css" rel="stylesheet">
{{end}}

{{define "content"}}
<!-- 页面标题和操作按钮 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1><i class="bi bi-hdd-network me-2"></i>节点管理</h1>
            <p class="subtitle">管理和监控蜜罐节点</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addNodeModal">
                <i class="bi bi-plus-circle me-2"></i>添加节点
            </button>
            <button class="btn btn-outline-secondary" onclick="refreshNodes()">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-left-primary h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总节点数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-nodes">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-hdd-network text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card success h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            在线节点
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-nodes">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-check-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            离线节点
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="offline-nodes">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-x-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card info h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            活跃部署
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-deployments">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-play-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="搜索节点名称或IP...">
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="status-filter">
                    <option value="">所有状态</option>
                    <option value="online">在线</option>
                    <option value="offline">离线</option>
                    <option value="error">错误</option>
                </select>
            </div>
            <div class="col-lg-3 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="region-filter">
                    <option value="">所有区域</option>
                    <option value="local">本地</option>
                    <option value="cloud">云端</option>
                </select>
            </div>
            <div class="col-lg-3 text-end">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-success btn-sm" onclick="batchStart()" title="批量启动">
                        <i class="bi bi-play me-1"></i>启动
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="batchStop()" title="批量停止">
                        <i class="bi bi-pause me-1"></i>停止
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="batchDelete()" title="批量删除">
                        <i class="bi bi-trash me-1"></i>删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 节点列表 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">节点列表</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportNodes()">
                        <i class="bi bi-download me-2"></i>导出数据
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="refreshAll()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新所有状态
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table" id="nodes-table">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        <th>名称</th>
                        <th>IP地址</th>
                        <th>状态</th>
                        <th>CPU</th>
                        <th>内存</th>
                        <th>部署数</th>
                        <th>最后心跳</th>
                        <th width="150">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="9" class="text-center">
                            <div class="loading">
                                <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="节点分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 添加节点模态框 -->
<div class="modal fade" id="addNodeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>添加节点
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="add-node-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="node-name" class="form-label">节点名称</label>
                                <input type="text" class="form-control" id="node-name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="node-ip" class="form-label">IP地址</label>
                                <input type="text" class="form-control" id="node-ip" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="node-port" class="form-label">端口</label>
                                <input type="number" class="form-control" id="node-port" value="2376">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="node-region" class="form-label">区域</label>
                                <select class="form-select" id="node-region">
                                    <option value="local">本地</option>
                                    <option value="cloud">云端</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="node-description" class="form-label">描述</label>
                        <textarea class="form-control" id="node-description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addNode()">添加节点</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script src="/static/js/nodes.js"></script>
{{end}}
