{{template "base.html" .}}

{{define "head"}}
<link href="/static/css/dashboard.css" rel="stylesheet">
{{end}}

{{define "content"}}
<!-- 页面标题和操作按钮 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1><i class="bi bi-shield-exclamation me-2"></i>情报数据</h1>
            <p class="subtitle">查看和分析攻击情报数据</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" onclick="exportIntelligence()">
                <i class="bi bi-download me-2"></i>导出数据
            </button>
            <button class="btn btn-outline-secondary" onclick="refreshIntelligence()">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-left-primary h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总攻击数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-attacks">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-shield-exclamation text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            今日攻击
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-attacks">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-exclamation-triangle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card danger h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            恶意IP
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="malicious-ips">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-geo-alt text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card info h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            攻击类型
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="attack-types">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-bug text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-graph-up me-2"></i>攻击趋势
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="attack-trend-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="bi bi-pie-chart me-2"></i>攻击类型分布
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-pie">
                    <canvas id="attack-type-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-lg-3 col-md-6 mb-3 mb-lg-0">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="搜索IP或攻击类型...">
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="type-filter">
                    <option value="">所有类型</option>
                    <option value="ssh">SSH攻击</option>
                    <option value="web">Web攻击</option>
                    <option value="ftp">FTP攻击</option>
                    <option value="other">其他</option>
                </select>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="severity-filter">
                    <option value="">所有级别</option>
                    <option value="high">高危</option>
                    <option value="medium">中危</option>
                    <option value="low">低危</option>
                </select>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <input type="date" class="form-control" id="date-filter">
            </div>
            <div class="col-lg-3 text-end">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary btn-sm" onclick="generateReport()" title="生成报告">
                        <i class="bi bi-file-earmark-text me-1"></i>报告
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearFilters()" title="清除筛选">
                        <i class="bi bi-x-circle me-1"></i>清除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 情报数据表格 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">攻击记录</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="exportCSV()">
                        <i class="bi bi-filetype-csv me-2"></i>导出CSV
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="exportJSON()">
                        <i class="bi bi-filetype-json me-2"></i>导出JSON
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新数据
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table" id="intelligence-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>源IP</th>
                        <th>目标</th>
                        <th>攻击类型</th>
                        <th>严重程度</th>
                        <th>地理位置</th>
                        <th>详情</th>
                        <th width="100">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="8" class="text-center">
                            <div class="loading">
                                <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <nav aria-label="情报数据分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 攻击详情模态框 -->
<div class="modal fade" id="attackDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-info-circle me-2"></i>攻击详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="attack-detail-content">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="blockIP()">封禁IP</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script src="/static/js/intelligence.js"></script>
{{end}}
