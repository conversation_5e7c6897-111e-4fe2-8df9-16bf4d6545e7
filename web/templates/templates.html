{{template "base.html" .}}

{{define "head"}}
<link href="/static/css/dashboard.css" rel="stylesheet">
{{end}}

{{define "content"}}
<!-- 页面标题和操作按钮 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1><i class="bi bi-layers me-2"></i>模板管理</h1>
            <p class="subtitle">管理蜜罐服务模板</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                <i class="bi bi-plus-circle me-2"></i>创建模板
            </button>
            <button class="btn btn-outline-secondary" onclick="refreshTemplates()">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card border-left-primary h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总模板数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-templates">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-layers text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card success h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            可用模板
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="available-templates">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-check-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card warning h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            使用中
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="used-templates">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-play-circle text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card stats-card info h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            自定义模板
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="custom-templates">
                            <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                        </div>
                    </div>
                    <div class="ms-3">
                        <i class="bi bi-gear text-gray-300" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-lg-4 col-md-6 mb-3 mb-lg-0">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" id="search-input" placeholder="搜索模板名称...">
                </div>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="category-filter">
                    <option value="">所有分类</option>
                    <option value="web">Web服务</option>
                    <option value="ssh">SSH服务</option>
                    <option value="ftp">FTP服务</option>
                    <option value="database">数据库</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
            <div class="col-lg-2 col-md-6 mb-3 mb-lg-0">
                <select class="form-select" id="status-filter">
                    <option value="">所有状态</option>
                    <option value="available">可用</option>
                    <option value="deprecated">已弃用</option>
                </select>
            </div>
            <div class="col-lg-4 text-end">
                <div class="btn-group" role="group">
                    <button class="btn btn-outline-primary btn-sm" onclick="importTemplate()" title="导入模板">
                        <i class="bi bi-upload me-1"></i>导入
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="exportTemplates()" title="导出模板">
                        <i class="bi bi-download me-1"></i>导出
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 模板列表 -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">模板列表</h6>
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="bi bi-three-dots"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#" onclick="refreshAll()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新所有
                    </a></li>
                    <li><a class="dropdown-item" href="#" onclick="validateAll()">
                        <i class="bi bi-check-circle me-2"></i>验证所有模板
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row" id="templates-grid">
            <!-- 模板卡片将通过JavaScript动态生成 -->
            <div class="col-12 text-center">
                <div class="loading">
                    <span class="spinner-border spinner-border-sm me-2"></span>加载中...
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <nav aria-label="模板分页" class="mt-4">
            <ul class="pagination justify-content-center" id="pagination">
                <!-- 分页按钮将通过JavaScript生成 -->
            </ul>
        </nav>
    </div>
</div>

<!-- 创建模板模态框 -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>创建模板
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="create-template-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template-name" class="form-label">模板名称</label>
                                <input type="text" class="form-control" id="template-name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template-category" class="form-label">分类</label>
                                <select class="form-select" id="template-category" required>
                                    <option value="">选择分类</option>
                                    <option value="web">Web服务</option>
                                    <option value="ssh">SSH服务</option>
                                    <option value="ftp">FTP服务</option>
                                    <option value="database">数据库</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template-image" class="form-label">Docker镜像</label>
                                <input type="text" class="form-control" id="template-image" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="template-version" class="form-label">版本</label>
                                <input type="text" class="form-control" id="template-version" value="latest">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template-description" class="form-label">描述</label>
                        <textarea class="form-control" id="template-description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template-ports" class="form-label">默认端口</label>
                        <input type="text" class="form-control" id="template-ports" 
                               placeholder="例如: 80,443,22">
                        <div class="form-text">多个端口用逗号分隔</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="template-env" class="form-label">环境变量</label>
                        <textarea class="form-control" id="template-env" rows="4" 
                                  placeholder='{"ENV_VAR": "default_value"}'></textarea>
                        <div class="form-text">JSON格式的默认环境变量</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTemplate()">创建模板</button>
            </div>
        </div>
    </div>
</div>
{{end}}

{{define "scripts"}}
<script src="/static/js/templates.js"></script>
{{end}}
