<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{if .Title}}{{.Title}} - {{end}}蜜罐管理平台</title>

    <!-- Bootstrap CSS -->
    <link href="/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="/static/libs/chart.js/chart.min.js"></script>
    <!-- Custom CSS -->
    <link href="/static/css/main.css" rel="stylesheet">

    {{block "head" .}}{{end}}
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-shield-check me-2"></i>
                蜜罐管理平台
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "dashboard"}}active{{end}}" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "nodes"}}active{{end}}" href="/nodes">
                            <i class="bi bi-hdd-network me-1"></i>节点管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "templates"}}active{{end}}" href="/templates">
                            <i class="bi bi-layers me-1"></i>模板管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "deployments"}}active{{end}}" href="/deployments">
                            <i class="bi bi-play-circle me-1"></i>部署管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{if eq .ActivePage "intelligence"}}active{{end}}" href="/intelligence">
                            <i class="bi bi-shield-exclamation me-1"></i>情报数据
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    {{if .User}}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span id="username">{{.User.Username}}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a></li>
                            <li><a class="dropdown-item" href="/settings">
                                <i class="bi bi-gear me-2"></i>系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logout-btn">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </li>
                    {{else}}
                    <li class="nav-item">
                        <a class="nav-link" href="/login">
                            <i class="bi bi-box-arrow-in-right me-1"></i>登录
                        </a>
                    </li>
                    {{end}}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 侧边栏 -->
    {{if .ShowSidebar}}
    <nav class="sidebar d-none d-lg-block" id="sidebar">
        <div class="sidebar-sticky">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "dashboard"}}active{{end}}" href="/dashboard">
                        <i class="bi bi-house"></i> 概览
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "realtime"}}active{{end}}" href="/realtime">
                        <i class="bi bi-eye"></i> 实时监控
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">
                <span>节点管理</span>
            </h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "nodes"}}active{{end}}" href="/nodes">
                        <i class="bi bi-hdd-network"></i> 节点列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "node-status"}}active{{end}}" href="/nodes/status">
                        <i class="bi bi-heart-pulse"></i> 状态监控
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">
                <span>服务管理</span>
            </h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "templates"}}active{{end}}" href="/templates">
                        <i class="bi bi-layers"></i> 模板管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "deployments"}}active{{end}}" href="/deployments">
                        <i class="bi bi-play-circle"></i> 部署管理
                    </a>
                </li>
            </ul>

            <h6 class="sidebar-heading">
                <span>数据分析</span>
            </h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "intelligence"}}active{{end}}" href="/intelligence">
                        <i class="bi bi-shield-exclamation"></i> 情报数据
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "statistics"}}active{{end}}" href="/statistics">
                        <i class="bi bi-graph-up"></i> 统计分析
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "reports"}}active{{end}}" href="/reports">
                        <i class="bi bi-file-earmark-text"></i> 报表生成
                    </a>
                </li>
            </ul>

            {{if and .User (eq .User.Role "administrator")}}
            <h6 class="sidebar-heading">
                <span>系统管理</span>
            </h6>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "users"}}active{{end}}" href="/users">
                        <i class="bi bi-people"></i> 用户管理
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "settings"}}active{{end}}" href="/settings">
                        <i class="bi bi-gear"></i> 系统设置
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {{if eq .ActivePage "logs"}}active{{end}}" href="/logs">
                        <i class="bi bi-list-ul"></i> 系统日志
                    </a>
                </li>
            </ul>
            {{end}}
        </div>
    </nav>
    {{end}}

    <!-- 主内容区域 -->
    <main class="{{if .ShowSidebar}}main-with-sidebar{{else}}main-full-width{{end}}"
        <!-- 面包屑导航 -->
        {{if .Breadcrumb}}
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                {{range .Breadcrumb}}
                <li class="breadcrumb-item {{if .Active}}active{{end}}">
                    {{if .URL}}<a href="{{.URL}}">{{.Name}}</a>{{else}}{{.Name}}{{end}}
                </li>
                {{end}}
            </ol>
        </nav>
        {{end}}

        <!-- 页面标题 -->
        {{if .PageHeader}}
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h1>{{.PageHeader}}</h1>
                    {{if .PageSubtitle}}
                    <p class="subtitle">{{.PageSubtitle}}</p>
                    {{end}}
                </div>
                {{if .PageActions}}
                <div class="d-flex gap-2">
                    {{range .PageActions}}
                    <a href="{{.URL}}" class="btn {{.Class}}">
                        {{if .Icon}}<i class="{{.Icon}} me-2"></i>{{end}}{{.Text}}
                    </a>
                    {{end}}
                </div>
                {{end}}
            </div>
        </div>
        {{end}}

        <!-- 消息提示 -->
        <div id="flash-messages">
            {{if .SuccessMessage}}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle me-2"></i>{{.SuccessMessage}}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {{end}}

            {{if .ErrorMessage}}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-circle me-2"></i>{{.ErrorMessage}}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {{end}}

            {{if .InfoMessage}}
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="bi bi-info-circle me-2"></i>{{.InfoMessage}}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {{end}}

            {{if .WarningMessage}}
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>{{.WarningMessage}}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {{end}}
        </div>

        <!-- 页面内容 -->
        {{block "content" .}}{{end}}
    </main>

    <!-- 页脚 -->
    <footer class="footer bg-light border-top">
        <div class="container-fluid">
            <div class="row align-items-center py-3">
                <div class="col-md-6">
                    <span class="text-muted">© 2024 蜜罐管理平台. All rights reserved.</span>
                </div>
                <div class="col-md-6 text-end">
                    <span class="text-muted">Version 1.0.0</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- 公共JavaScript -->
    <script src="/static/js/main.js"></script>

    {{block "scripts" .}}{{end}}
</body>
</html>
