/* ===================================
   登录页面专用样式
   =================================== */

/* 登录页面布局 */
.login-page {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  font-family: var(--font-family-sans);
}

/* 登录容器 */
.login-container {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  max-width: 420px;
  width: 100%;
  animation: fadeInUp 0.6s ease-out;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

/* 登录头部 */
.login-header {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
  color: white;
  padding: var(--space-10) var(--space-8);
  text-align: center;
  position: relative;
}

.login-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.login-header > * {
  position: relative;
  z-index: 1;
}

.login-header .icon {
  font-size: 3.5rem;
  margin-bottom: var(--space-4);
  display: block;
}

.login-header h2 {
  margin: 0 0 var(--space-2);
  font-weight: 700;
  font-size: var(--text-2xl);
}

.login-header p {
  margin: 0;
  opacity: 0.9;
  font-size: var(--text-sm);
}

/* 登录表单 */
.login-form {
  padding: var(--space-8);
}

.form-floating {
  margin-bottom: var(--space-5);
  position: relative;
}

.form-floating .form-control {
  height: 3.5rem;
  padding: var(--space-4) var(--space-4) var(--space-3);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: var(--transition-fast);
  background: var(--bg-primary);
}

.form-floating .form-control:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

.form-floating label {
  padding: var(--space-4);
  color: var(--gray-500);
  font-weight: 500;
  transition: var(--transition-fast);
}

.form-floating .form-control:focus ~ label,
.form-floating .form-control:not(:placeholder-shown) ~ label {
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: var(--primary-600);
}

/* 记住我复选框 */
.form-check {
  margin-bottom: var(--space-6);
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-sm);
}

.form-check-input:checked {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
}

.form-check-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-left: var(--space-2);
}

/* 登录按钮 */
.btn-login {
  width: 100%;
  height: 3.5rem;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  font-weight: 600;
  font-size: var(--text-base);
  transition: var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-login::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.6s;
}

.btn-login:hover::before {
  left: 100%;
}

.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
}

.btn-login:active {
  transform: translateY(0);
}

/* 忘记密码链接 */
.forgot-password {
  text-align: center;
  margin-top: var(--space-6);
}

.forgot-password a {
  color: var(--primary-500);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
}

.forgot-password a:hover {
  color: var(--primary-600);
  text-decoration: underline;
}

/* 系统信息 */
.system-info {
  background: var(--gray-50);
  padding: var(--space-5);
  text-align: center;
  font-size: var(--text-sm);
  color: var(--gray-600);
  border-top: 1px solid var(--border-color);
}

.system-info .row > div {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

/* 警告框样式 */
.alert {
  border-radius: var(--radius-lg);
  border: none;
  animation: slideDown 0.3s ease-out;
}

.alert-danger {
  background-color: var(--danger-light);
  color: #721c24;
}

.alert-info {
  background-color: var(--info-light);
  color: #055160;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载动画 */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* 表单验证样式 */
.is-invalid {
  border-color: var(--danger-color) !important;
}

.is-valid {
  border-color: var(--success-color) !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    margin: var(--space-4);
    border-radius: var(--radius-xl);
  }

  .login-header {
    padding: var(--space-8) var(--space-6);
  }

  .login-form {
    padding: var(--space-6);
  }

  .login-header .icon {
    font-size: 2.5rem;
  }

  .login-header h2 {
    font-size: var(--text-xl);
  }
}
