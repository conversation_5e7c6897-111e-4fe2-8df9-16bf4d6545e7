/* ===================================
   CSS变量系统 - 设计令牌
   =================================== */
:root {
  /* 主色调系统 */
  --primary-50: #f0f4ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #667eea;
  --primary-600: #5b21b6;
  --primary-700: #4c1d95;
  --primary-800: #3730a3;
  --primary-900: #312e81;

  /* 语义化颜色 */
  --success-color: #1cc88a;
  --success-light: #d4edda;
  --warning-color: #f6c23e;
  --warning-light: #fff3cd;
  --danger-color: #e74a3b;
  --danger-light: #f8d7da;
  --info-color: #36b9cc;
  --info-light: #d1ecf1;

  /* 中性色系统 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 背景色系统 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fc;
  --bg-tertiary: #f1f3f4;
  --bg-overlay: rgba(0, 0, 0, 0.5);

  /* 边框和分割线 */
  --border-color: #e3e6f0;
  --border-light: #f1f3f4;
  --border-dark: #d1d5db;

  /* 阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 圆角系统 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;

  /* 字体系统 */
  --font-family-sans: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

  /* 字体大小 */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;

  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* 过渡动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ===================================
   全局基础样式
   =================================== */

/* 重置和基础样式 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--gray-800);
  background-color: var(--bg-secondary);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--primary-500);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-600);
  text-decoration: underline;
}

a:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: var(--leading-tight);
  margin-top: 0;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

h1 { font-size: var(--text-3xl); }
h2 { font-size: var(--text-2xl); }
h3 { font-size: var(--text-xl); }
h4 { font-size: var(--text-lg); }
h5 { font-size: var(--text-base); }
h6 { font-size: var(--text-sm); }

/* 段落和文本 */
p {
  margin-top: 0;
  margin-bottom: var(--space-4);
}

/* 列表样式 */
ul, ol {
  margin-top: 0;
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
}

/* 代码样式 */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--gray-100);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--gray-100);
  padding: var(--space-4);
  border-radius: var(--radius-md);
  overflow-x: auto;
}

/* 表格基础样式 */
table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  text-align: left;
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: 600;
  color: var(--gray-700);
}

/* ===================================
   导航组件样式
   =================================== */

/* 顶部导航栏 */
.navbar {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
  box-shadow: var(--shadow-sm);
  border: none;
  padding: var(--space-3) 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: var(--text-xl);
  color: white !important;
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9) !important;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  margin: 0 var(--space-1);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.2);
  font-weight: 600;
}

/* 侧边栏样式 */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  background: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-fixed);
  padding-top: 80px; /* 为顶部导航栏留出空间 */
  overflow-y: auto;
  transition: var(--transition-normal);
}

.sidebar-sticky {
  position: sticky;
  top: 0;
  padding: var(--space-6) 0;
}

/* 侧边栏导航链接 */
.sidebar .nav-link {
  font-weight: 500;
  color: var(--gray-700);
  padding: var(--space-3) var(--space-6);
  border-radius: 0;
  margin: 0;
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  border-left: 3px solid transparent;
}

.sidebar .nav-link:hover {
  color: var(--primary-600);
  background-color: var(--primary-50);
  border-left-color: var(--primary-200);
  text-decoration: none;
}

.sidebar .nav-link.active {
  color: var(--primary-700);
  background-color: var(--primary-100);
  border-left-color: var(--primary-500);
  font-weight: 600;
}

.sidebar .nav-link i {
  width: 20px;
  text-align: center;
  font-size: var(--text-base);
}

/* 侧边栏分组标题 */
.sidebar-heading {
  font-size: var(--text-xs);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--gray-500);
  padding: var(--space-4) var(--space-6) var(--space-2);
  margin-top: var(--space-6);
}

.sidebar-heading:first-child {
  margin-top: 0;
}

/* 主内容区域 */
.main-with-sidebar {
  margin-left: 280px;
  padding: var(--space-6);
  min-height: calc(100vh - 80px); /* 减去导航栏高度 */
  background-color: var(--bg-secondary);
  padding-top: 100px; /* 为固定导航栏留出空间 */
}

.main-full-width {
  padding: var(--space-6);
  min-height: calc(100vh - 80px);
  background-color: var(--bg-secondary);
  padding-top: 100px; /* 为固定导航栏留出空间 */
}

/* 页脚样式 */
.footer {
  margin-top: auto;
  padding: var(--space-4) 0;
  background-color: var(--bg-primary);
  border-top: 1px solid var(--border-color);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  main {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .navbar-nav .nav-link {
    padding: var(--space-3) var(--space-4);
  }

  main {
    padding: var(--space-4);
  }
}

/* ===================================
   卡片组件样式
   =================================== */

.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-4) var(--space-6);
  font-weight: 600;
  color: var(--gray-800);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  background: var(--gray-50);
  border-top: 1px solid var(--border-color);
  padding: var(--space-4) var(--space-6);
}

/* 统计卡片样式 */
.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary-500);
}

.stats-card.success::before { background: var(--success-color); }
.stats-card.warning::before { background: var(--warning-color); }
.stats-card.danger::before { background: var(--danger-color); }
.stats-card.info::before { background: var(--info-color); }

/* ===================================
   工具类样式
   =================================== */

/* 边框工具类 */
.border-left-primary { border-left: 4px solid var(--primary-500) !important; }
.border-left-success { border-left: 4px solid var(--success-color) !important; }
.border-left-warning { border-left: 4px solid var(--warning-color) !important; }
.border-left-danger { border-left: 4px solid var(--danger-color) !important; }
.border-left-info { border-left: 4px solid var(--info-color) !important; }

/* 文本颜色工具类 */
.text-primary { color: var(--primary-500) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-info { color: var(--info-color) !important; }

.text-gray-50 { color: var(--gray-50) !important; }
.text-gray-100 { color: var(--gray-100) !important; }
.text-gray-200 { color: var(--gray-200) !important; }
.text-gray-300 { color: var(--gray-300) !important; }
.text-gray-400 { color: var(--gray-400) !important; }
.text-gray-500 { color: var(--gray-500) !important; }
.text-gray-600 { color: var(--gray-600) !important; }
.text-gray-700 { color: var(--gray-700) !important; }
.text-gray-800 { color: var(--gray-800) !important; }
.text-gray-900 { color: var(--gray-900) !important; }

.text-muted { color: var(--gray-500) !important; }

/* 字体大小工具类 */
.text-xs { font-size: var(--text-xs) !important; }
.text-sm { font-size: var(--text-sm) !important; }
.text-base { font-size: var(--text-base) !important; }
.text-lg { font-size: var(--text-lg) !important; }
.text-xl { font-size: var(--text-xl) !important; }
.text-2xl { font-size: var(--text-2xl) !important; }
.text-3xl { font-size: var(--text-3xl) !important; }

/* 字体粗细工具类 */
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }
.font-extrabold { font-weight: 800 !important; }

/* 兼容性别名 */
.font-weight-bold { font-weight: 700 !important; }

/* 背景颜色工具类 */
.bg-primary { background-color: var(--primary-500) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

.bg-light { background-color: var(--gray-100) !important; }
.bg-dark { background-color: var(--gray-800) !important; }

/* ===================================
   图表组件样式
   =================================== */

.chart-area {
  position: relative;
  height: 300px;
  width: 100%;
  padding: var(--space-4);
}

.chart-pie {
  position: relative;
  height: 400px;
  width: 100%;
  padding: var(--space-4);
}

.chart-bar {
  position: relative;
  height: 350px;
  width: 100%;
  padding: var(--space-4);
}

@media (max-width: 768px) {
  .chart-area {
    height: 250px;
  }

  .chart-pie {
    height: 300px;
  }

  .chart-bar {
    height: 280px;
  }
}

/* ===================================
   表格组件样式
   =================================== */

.table {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.table thead th {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  border: none;
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  text-transform: uppercase;
  font-size: var(--text-xs);
  letter-spacing: 0.05em;
  color: var(--gray-700);
  padding: var(--space-4) var(--space-6);
  white-space: nowrap;
}

.table tbody td {
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--border-light);
  vertical-align: middle;
}

.table tbody tr {
  transition: var(--transition-fast);
}

.table tbody tr:hover {
  background-color: var(--primary-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* 表格响应式 */
.table-responsive {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

@media (max-width: 768px) {
  .table thead th,
  .table tbody td {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
  }

  .table thead th {
    font-size: 0.7rem;
  }
}

/* ===================================
   按钮组件样式
   =================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-5);
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(var(--primary-500), 0.1);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮大小变体 */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-base);
}

/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  color: white;
  border-color: var(--primary-500);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
  color: white;
}

/* 成功按钮 */
.btn-success {
  background-color: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn-success:hover {
  background-color: #17a673;
  border-color: #17a673;
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 警告按钮 */
.btn-warning {
  background-color: var(--warning-color);
  color: var(--gray-800);
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background-color: #f4b619;
  border-color: #f4b619;
  color: var(--gray-800);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 危险按钮 */
.btn-danger {
  background-color: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 信息按钮 */
.btn-info {
  background-color: var(--info-color);
  color: white;
  border-color: var(--info-color);
}

.btn-info:hover {
  background-color: #2c9faf;
  border-color: #2c9faf;
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 轮廓按钮 */
.btn-outline-primary {
  color: var(--primary-500);
  border-color: var(--primary-500);
  background: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary-500);
  color: white;
}

.btn-outline-secondary {
  color: var(--gray-600);
  border-color: var(--gray-300);
  background: transparent;
}

.btn-outline-secondary:hover {
  background-color: var(--gray-600);
  color: white;
  border-color: var(--gray-600);
}

/* 徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: 600;
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-primary { background-color: var(--primary-100); color: var(--primary-700); }
.badge-success { background-color: var(--success-light); color: #0f5132; }
.badge-warning { background-color: var(--warning-light); color: #664d03; }
.badge-danger { background-color: var(--danger-light); color: #721c24; }
.badge-info { background-color: var(--info-light); color: #055160; }

/* ===================================
   页面布局组件
   =================================== */

/* 面包屑导航 */
.breadcrumb {
  background-color: transparent;
  padding: 0;
  margin-bottom: var(--space-6);
  font-size: var(--text-sm);
}

.breadcrumb-item {
  color: var(--gray-600);
}

.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: var(--gray-400);
  margin: 0 var(--space-2);
}

.breadcrumb-item.active {
  color: var(--gray-800);
  font-weight: 500;
}

/* 页面标题 */
.page-header {
  margin-bottom: var(--space-8);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid var(--border-color);
}

.page-header h1 {
  color: var(--gray-900);
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.page-header .subtitle {
  color: var(--gray-600);
  font-size: var(--text-base);
  margin: 0;
}

/* ===================================
   状态指示器组件
   =================================== */

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
  font-weight: 500;
}

.status-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-online::before {
  background-color: var(--success-color);
  box-shadow: 0 0 0 2px rgba(28, 200, 138, 0.2);
}

.status-offline::before {
  background-color: var(--danger-color);
  box-shadow: 0 0 0 2px rgba(231, 74, 59, 0.2);
}

.status-warning::before {
  background-color: var(--warning-color);
  box-shadow: 0 0 0 2px rgba(246, 194, 62, 0.2);
}

.status-pending::before {
  background-color: var(--info-color);
  box-shadow: 0 0 0 2px rgba(54, 185, 204, 0.2);
}

/* ===================================
   加载和空状态组件
   =================================== */

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  color: var(--gray-500);
  text-align: center;
}

.loading i {
  font-size: 2.5rem;
  margin-bottom: var(--space-4);
  color: var(--primary-400);
}

.loading .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: var(--space-4);
  color: var(--primary-500);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  color: var(--gray-500);
  text-align: center;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: var(--space-6);
  color: var(--gray-300);
}

.empty-state h4 {
  color: var(--gray-700);
  font-weight: 600;
  margin-bottom: var(--space-2);
}

.empty-state p {
  color: var(--gray-500);
  margin-bottom: var(--space-6);
  max-width: 400px;
}

/* ===================================
   表单组件样式
   =================================== */

.form-control {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  transition: var(--transition-fast);
  background-color: var(--bg-primary);
}

.form-control:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

.form-control:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

.form-select {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-sm);
  transition: var(--transition-fast);
  background-color: var(--bg-primary);
}

.form-select:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

.form-label {
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-text {
  font-size: var(--text-xs);
  color: var(--gray-500);
  margin-top: var(--space-1);
}

/* ===================================
   模态框组件样式
   =================================== */

.modal-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
  border-bottom: 1px solid var(--border-color);
  padding: var(--space-5) var(--space-6);
}

.modal-title {
  font-weight: 600;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  background: var(--gray-50);
  border-top: 1px solid var(--border-color);
  padding: var(--space-4) var(--space-6);
  display: flex;
  gap: var(--space-3);
  justify-content: flex-end;
}

/* ===================================
   分页组件样式
   =================================== */

.pagination {
  display: flex;
  gap: var(--space-1);
}

.pagination .page-link {
  color: var(--primary-600);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  text-decoration: none;
  transition: var(--transition-fast);
  font-weight: 500;
}

.pagination .page-link:hover {
  color: var(--primary-700);
  background-color: var(--primary-50);
  border-color: var(--primary-200);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
  color: white;
}

.pagination .page-item.disabled .page-link {
  color: var(--gray-400);
  background-color: var(--gray-100);
  border-color: var(--border-color);
  cursor: not-allowed;
}

/* ===================================
   进度条组件样式
   =================================== */

.progress {
  height: 8px;
  background-color: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  background: linear-gradient(90deg, var(--primary-500) 0%, var(--primary-600) 100%);
  transition: width 0.6s ease;
}

.progress-bar.bg-success {
  background: linear-gradient(90deg, var(--success-color) 0%, #17a673 100%);
}

.progress-bar.bg-warning {
  background: linear-gradient(90deg, var(--warning-color) 0%, #f4b619 100%);
}

.progress-bar.bg-danger {
  background: linear-gradient(90deg, var(--danger-color) 0%, #dc3545 100%);
}

/* ===================================
   工具提示样式
   =================================== */

.tooltip {
  font-size: var(--text-xs);
  border-radius: var(--radius-md);
}

.tooltip-inner {
  background-color: var(--gray-800);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
}

/* ===================================
   自定义滚动条
   =================================== */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* ===================================
   动画效果
   =================================== */

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* ===================================
   响应式设计
   =================================== */

@media (max-width: 1200px) {
  .container-fluid {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }
}

@media (max-width: 768px) {
  .card-body {
    padding: var(--space-4);
  }

  .modal-body {
    padding: var(--space-4);
  }

  .modal-header,
  .modal-footer {
    padding: var(--space-4);
  }

  .page-header {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
  }

  .page-header h1 {
    font-size: var(--text-2xl);
  }
}

@media (max-width: 480px) {
  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
  }

  .card {
    margin-bottom: var(--space-4);
  }

  .table thead th,
  .table tbody td {
    padding: var(--space-2) var(--space-3);
    font-size: var(--text-xs);
  }
}
