/**
 * 登录页面JavaScript模块
 * 处理用户登录、表单验证、状态管理等功能
 */

// 登录管理器
class LoginManager {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.usernameInput = document.getElementById('username');
        this.passwordInput = document.getElementById('password');
        this.rememberCheckbox = document.getElementById('remember');
        this.submitButton = this.form?.querySelector('button[type="submit"]');
        this.alertContainer = document.getElementById('alert-container');

        this.init();
    }

    // 初始化
    init() {
        if (!this.form) return;

        this.bindEvents();
        this.checkExistingLogin();
        this.loadRememberedCredentials();
        this.focusFirstInput();
    }

    // 绑定事件
    bindEvents() {
        // 表单提交事件
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));

        // 输入框事件
        [this.usernameInput, this.passwordInput].forEach(input => {
            if (input) {
                input.addEventListener('input', () => this.clearValidation(input));
                input.addEventListener('blur', () => this.validateField(input));
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.clearAlerts();
            }
        });
    }

    // 表单提交处理
    async handleSubmit(e) {
        e.preventDefault();

        this.clearAlerts();

        if (!this.validateForm()) {
            return;
        }

        this.setLoading(true);

        try {
            const formData = this.getFormData();
            const result = await this.performLogin(formData);

            if (result.success) {
                this.handleLoginSuccess(result.data, formData.remember);
            } else {
                this.showAlert(result.message || '登录失败', 'danger');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('网络错误，请稍后重试', 'danger');
        } finally {
            this.setLoading(false);
        }
    }

    // 表单验证
    validateForm() {
        const username = this.usernameInput.value.trim();
        const password = this.passwordInput.value.trim();

        if (!username) {
            this.showAlert('请输入用户名', 'danger');
            this.usernameInput.focus();
            return false;
        }

        if (username.length < 2) {
            this.showAlert('用户名至少需要2个字符', 'danger');
            this.usernameInput.focus();
            return false;
        }

        if (!password) {
            this.showAlert('请输入密码', 'danger');
            this.passwordInput.focus();
            return false;
        }

        if (password.length < 4) {
            this.showAlert('密码至少需要4个字符', 'danger');
            this.passwordInput.focus();
            return false;
        }

        return true;
    }

    // 验证单个字段
    validateField(input) {
        const value = input.value.trim();
        if (!value) return;

        let isValid = false;

        if (input === this.usernameInput) {
            isValid = value.length >= 2;
        } else if (input === this.passwordInput) {
            isValid = value.length >= 4;
        }

        input.classList.toggle('is-valid', isValid);
        input.classList.toggle('is-invalid', !isValid);
    }

    // 清除字段验证状态
    clearValidation(input) {
        input.classList.remove('is-valid', 'is-invalid');
    }

    // 获取表单数据
    getFormData() {
        return {
            username: this.usernameInput.value.trim(),
            password: this.passwordInput.value.trim(),
            remember: this.rememberCheckbox.checked
        };
    }

    // 执行登录请求
    async performLogin(formData) {
        const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: formData.username,
                password: formData.password,
                remember: formData.remember
            })
        });

        const data = await response.json();

        return {
            success: response.ok && data.success,
            data: data.data,
            message: data.message
        };
    }

    // 处理登录成功
    handleLoginSuccess(data, remember) {
        // 保存认证信息
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user_info));

        // 处理记住我功能
        if (remember) {
            localStorage.setItem('remember_me', 'true');
            localStorage.setItem('username', this.usernameInput.value.trim());
        } else {
            localStorage.removeItem('remember_me');
            localStorage.removeItem('username');
        }

        // 显示成功消息
        this.showAlert('登录成功，正在跳转...', 'success');

        // 延迟跳转
        setTimeout(() => {
            window.location.href = '/dashboard';
        }, 1000);
    }

    // 设置加载状态
    setLoading(loading) {
        if (!this.submitButton) return;

        if (loading) {
            this.submitButton.disabled = true;
            this.submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>登录中...';
        } else {
            this.submitButton.disabled = false;
            this.submitButton.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>登录';
        }
    }

    // 显示提示消息
    showAlert(message, type = 'info') {
        this.clearAlerts();

        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;

        const iconMap = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        alertDiv.innerHTML = `
            <i class="bi bi-${iconMap[type]} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        this.alertContainer.appendChild(alertDiv);

        // 自动隐藏（除了成功消息）
        if (type !== 'success') {
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    }

    // 清除所有提示消息
    clearAlerts() {
        if (this.alertContainer) {
            this.alertContainer.innerHTML = '';
        }
    }

    // 加载记住的凭据
    loadRememberedCredentials() {
        const rememberedUsername = localStorage.getItem('username');
        const rememberMe = localStorage.getItem('remember_me');

        if (rememberMe === 'true' && rememberedUsername) {
            this.usernameInput.value = rememberedUsername;
            this.rememberCheckbox.checked = true;
        }
    }

    // 检查现有登录状态
    async checkExistingLogin() {
        const token = localStorage.getItem('token');
        if (!token) return;

        try {
            const response = await fetch('/api/v1/auth/profile', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                // Token有效，直接跳转
                window.location.href = '/dashboard';
            } else {
                // Token无效，清除存储
                this.clearAuthData();
            }
        } catch (error) {
            // 网络错误，清除存储
            this.clearAuthData();
        }
    }

    // 清除认证数据
    clearAuthData() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
    }

    // 聚焦第一个输入框
    focusFirstInput() {
        const rememberedUsername = localStorage.getItem('username');
        const rememberMe = localStorage.getItem('remember_me');

        if (rememberMe === 'true' && rememberedUsername) {
            this.passwordInput.focus();
        } else {
            this.usernameInput.focus();
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    new LoginManager();
});
