/**
 * 蜜罐管理平台 - 主JavaScript模块
 * 提供全局功能、API调用、认证管理等核心功能
 */

// ===================================
// 全局配置和常量
// ===================================

const CONFIG = {
    API_BASE_URL: '/api/v1',
    STORAGE_KEYS: {
        TOKEN: 'token',
        USER: 'user',
        REMEMBER_ME: 'remember_me',
        USERNAME: 'username'
    },
    ENDPOINTS: {
        AUTH: {
            LOGIN: '/auth/login',
            LOGOUT: '/auth/logout',
            PROFILE: '/auth/profile',
            REFRESH: '/auth/refresh'
        },
        NODES: '/nodes',
        TEMPLATES: '/templates',
        DEPLOYMENTS: '/deployments',
        INTELLIGENCE: '/intelligence'
    }
};

// ===================================
// 应用状态管理
// ===================================

class AppState {
    constructor() {
        this.currentUser = null;
        this.authToken = null;
        this.isAuthenticated = false;
    }

    // 设置用户信息
    setUser(user, token) {
        this.currentUser = user;
        this.authToken = token;
        this.isAuthenticated = true;

        // 更新UI
        this.updateUserUI();
    }

    // 清除用户信息
    clearUser() {
        this.currentUser = null;
        this.authToken = null;
        this.isAuthenticated = false;

        // 清除存储
        localStorage.removeItem(CONFIG.STORAGE_KEYS.TOKEN);
        localStorage.removeItem(CONFIG.STORAGE_KEYS.USER);
    }

    // 更新用户界面
    updateUserUI() {
        const usernameElements = document.querySelectorAll('#username');
        usernameElements.forEach(el => {
            if (this.currentUser) {
                el.textContent = this.currentUser.username || '用户';
            }
        });
    }

    // 检查认证状态
    checkAuth() {
        const token = localStorage.getItem(CONFIG.STORAGE_KEYS.TOKEN);
        const userStr = localStorage.getItem(CONFIG.STORAGE_KEYS.USER);

        if (token && userStr) {
            try {
                const user = JSON.parse(userStr);
                this.setUser(user, token);
                return true;
            } catch (e) {
                console.error('Failed to parse user data:', e);
                this.clearUser();
                return false;
            }
        }

        return false;
    }
}

// 全局应用状态实例
const appState = new AppState();

// ===================================
// API客户端类
// ===================================

class ApiClient {
    constructor() {
        this.baseURL = CONFIG.API_BASE_URL;
    }

    // 获取请求头
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };

        if (appState.authToken) {
            headers['Authorization'] = `Bearer ${appState.authToken}`;
        }

        return headers;
    }

    // 处理响应
    async handleResponse(response) {
        if (!response.ok) {
            if (response.status === 401) {
                // Token过期，重新登录
                appState.clearUser();
                window.location.href = '/login';
                throw new Error('认证失败，请重新登录');
            }

            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    }

    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(this.baseURL + endpoint, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== null && params[key] !== undefined) {
                url.searchParams.append(key, params[key]);
            }
        });

        const response = await fetch(url, {
            method: 'GET',
            headers: this.getHeaders()
        });

        return this.handleResponse(response);
    }

    // POST请求
    async post(endpoint, data = {}) {
        const response = await fetch(this.baseURL + endpoint, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(data)
        });

        return this.handleResponse(response);
    }

    // PUT请求
    async put(endpoint, data = {}) {
        const response = await fetch(this.baseURL + endpoint, {
            method: 'PUT',
            headers: this.getHeaders(),
            body: JSON.stringify(data)
        });

        return this.handleResponse(response);
    }

    // DELETE请求
    async delete(endpoint) {
        const response = await fetch(this.baseURL + endpoint, {
            method: 'DELETE',
            headers: this.getHeaders()
        });

        return this.handleResponse(response);
    }
}

// 全局API客户端实例
const api = new ApiClient();

// ===================================
// 认证管理
// ===================================

class AuthManager {
    // 验证Token
    async validateToken() {
        try {
            const result = await api.get(CONFIG.ENDPOINTS.AUTH.PROFILE);
            if (result.success) {
                appState.setUser(result.data, appState.authToken);
                return true;
            }
        } catch (error) {
            console.error('Token validation error:', error);
            this.logout();
            return false;
        }
    }

    // 登出
    logout() {
        appState.clearUser();
        window.location.href = '/login';
    }

    // 检查认证状态
    checkAuthStatus() {
        if (appState.checkAuth()) {
            // 验证Token有效性
            this.validateToken();
        } else {
            // 如果不在登录页面，重定向到登录页面
            if (!window.location.pathname.includes('/login')) {
                window.location.href = '/login';
            }
        }
    }
}

// 全局认证管理器实例
const authManager = new AuthManager();
// ===================================
// UI工具类
// ===================================

class UIUtils {
    // 显示提示消息
    static showAlert(message, type = 'info', duration = 5000) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;

        const iconMap = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };

        alertDiv.innerHTML = `
            <i class="bi bi-${iconMap[type]} me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 移除现有的提示
        const existingAlerts = document.querySelectorAll('#flash-messages .alert');
        existingAlerts.forEach(alert => alert.remove());

        // 添加新提示
        const flashContainer = document.getElementById('flash-messages');
        if (flashContainer) {
            flashContainer.appendChild(alertDiv);

            // 自动隐藏
            if (duration > 0) {
                setTimeout(() => {
                    alertDiv.remove();
                }, duration);
            }
        }
    }

    // 显示加载状态
    static showLoading(element, text = '加载中...') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }

        if (element) {
            element.innerHTML = `<span class="spinner-border spinner-border-sm me-2"></span>${text}`;
        }
    }

    // 显示空状态
    static showEmpty(element, message = '暂无数据') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }

        if (element) {
            element.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-inbox"></i>
                    <h4>暂无数据</h4>
                    <p>${message}</p>
                </div>
            `;
        }
    }

    // 确认对话框
    static confirm(message, title = '确认操作') {
        return new Promise((resolve) => {
            const result = window.confirm(`${title}\n\n${message}`);
            resolve(result);
        });
    }

    // 格式化日期
    static formatDate(dateString, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!dateString) return '-';

        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '-';

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    // 格式化文件大小
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}
    }
}

// ===================================
// 应用初始化
// ===================================

class App {
    constructor() {
        this.init();
    }

    // 初始化应用
    init() {
        // 检查认证状态
        authManager.checkAuthStatus();

        // 初始化UI组件
        this.initializeComponents();

        // 绑定全局事件
        this.bindGlobalEvents();
    }

    // 初始化UI组件
    initializeComponents() {
        // 初始化工具提示
        this.initializeTooltips();

        // 初始化模态框
        this.initializeModals();

        // 初始化表单验证
        this.initializeFormValidation();
    }

    // 初始化工具提示
    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // 初始化模态框
    initializeModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('hidden.bs.modal', function () {
                // 清理模态框内容
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                    // 清除验证状态
                    const inputs = form.querySelectorAll('.form-control');
                    inputs.forEach(input => {
                        input.classList.remove('is-valid', 'is-invalid');
                    });
                }
            });
        });
    }

    // 初始化表单验证
    initializeFormValidation() {
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    // 绑定全局事件
    bindGlobalEvents() {
        // 绑定登出按钮
        const logoutButtons = document.querySelectorAll('#logout-btn, [data-action="logout"]');
        logoutButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                authManager.logout();
            });
        });

        // 绑定确认删除按钮
        const deleteButtons = document.querySelectorAll('[data-action="delete"]');
        deleteButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();
                const message = button.getAttribute('data-message') || '确定要删除这个项目吗？';
                const confirmed = await UIUtils.confirm(message);
                if (confirmed) {
                    const url = button.getAttribute('href') || button.getAttribute('data-url');
                    if (url) {
                        this.deleteResource(url);
                    }
                }
            });
        });

        // 绑定复制按钮
        const copyButtons = document.querySelectorAll('[data-action="copy"]');
        copyButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const text = button.getAttribute('data-text') || button.textContent;
                this.copyToClipboard(text);
            });
        });
    }

    // 删除资源
    async deleteResource(url) {
        try {
            const result = await api.delete(url);
            if (result.success) {
                UIUtils.showAlert('删除成功', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                UIUtils.showAlert(result.message || '删除失败', 'danger');
            }
        } catch (error) {
            console.error('Delete error:', error);
            UIUtils.showAlert('删除失败: ' + error.message, 'danger');
        }
    }

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(text);
                UIUtils.showAlert('已复制到剪贴板', 'success', 2000);
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                UIUtils.showAlert('已复制到剪贴板', 'success', 2000);
            }
        } catch (error) {
            console.error('Copy failed:', error);
            UIUtils.showAlert('复制失败', 'danger');
        }
    }
}

// ===================================
// 全局函数（向后兼容）
// ===================================

// 显示提示消息
function showAlert(message, type = 'info', duration = 5000) {
    UIUtils.showAlert(message, type, duration);
}

// 显示加载状态
function showLoading(element, text = '加载中...') {
    UIUtils.showLoading(element, text);
}

// 格式化日期
function formatDate(dateString, format = 'YYYY-MM-DD HH:mm:ss') {
    return UIUtils.formatDate(dateString, format);
}

// 格式化文件大小
function formatFileSize(bytes) {
    return UIUtils.formatFileSize(bytes);
}

// 防抖函数
function debounce(func, wait) {
    return UIUtils.debounce(func, wait);
}

// ===================================
// 应用启动
// ===================================

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
    new App();
});
