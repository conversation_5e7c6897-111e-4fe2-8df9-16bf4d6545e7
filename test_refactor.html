<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重构测试页面</title>
    <link href="web/static/libs/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="web/static/libs/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="web/static/css/main.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
        }
        .test-result {
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: var(--radius-sm);
        }
        .test-pass {
            background-color: var(--success-light);
            color: #0f5132;
        }
        .test-fail {
            background-color: var(--danger-light);
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="bi bi-check-circle me-2"></i>蜜罐管理平台重构测试</h1>
        <p class="text-muted">验证重构后的CSS变量、组件样式和JavaScript模块是否正常工作</p>

        <!-- CSS变量测试 -->
        <div class="test-section">
            <h3>CSS变量系统测试</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="card stats-card border-left-primary">
                        <div class="card-body">
                            <div class="text-primary">主色调测试</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card success">
                        <div class="card-body">
                            <div class="text-success">成功色测试</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card warning">
                        <div class="card-body">
                            <div class="text-warning">警告色测试</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card stats-card danger">
                        <div class="card-body">
                            <div class="text-danger">危险色测试</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按钮组件测试 -->
        <div class="test-section">
            <h3>按钮组件测试</h3>
            <div class="d-flex gap-2 flex-wrap">
                <button class="btn btn-primary">主要按钮</button>
                <button class="btn btn-success">成功按钮</button>
                <button class="btn btn-warning">警告按钮</button>
                <button class="btn btn-danger">危险按钮</button>
                <button class="btn btn-info">信息按钮</button>
                <button class="btn btn-outline-primary">轮廓按钮</button>
            </div>
        </div>

        <!-- 表格组件测试 -->
        <div class="test-section">
            <h3>表格组件测试</h3>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>状态</th>
                            <th>类型</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>测试项目1</td>
                            <td><span class="badge badge-success">正常</span></td>
                            <td>Web服务</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">编辑</button>
                                <button class="btn btn-sm btn-outline-danger">删除</button>
                            </td>
                        </tr>
                        <tr>
                            <td>测试项目2</td>
                            <td><span class="badge badge-warning">警告</span></td>
                            <td>SSH服务</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">编辑</button>
                                <button class="btn btn-sm btn-outline-danger">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 表单组件测试 -->
        <div class="test-section">
            <h3>表单组件测试</h3>
            <form>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="test-input" class="form-label">测试输入框</label>
                            <input type="text" class="form-control" id="test-input" placeholder="请输入内容">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="test-select" class="form-label">测试选择框</label>
                            <select class="form-select" id="test-select">
                                <option>选项1</option>
                                <option>选项2</option>
                                <option>选项3</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="test-textarea" class="form-label">测试文本域</label>
                    <textarea class="form-control" id="test-textarea" rows="3"></textarea>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="test-checkbox">
                    <label class="form-check-label" for="test-checkbox">
                        测试复选框
                    </label>
                </div>
            </form>
        </div>

        <!-- JavaScript测试 -->
        <div class="test-section">
            <h3>JavaScript模块测试</h3>
            <div id="js-test-results">
                <div class="test-result test-pass">
                    <i class="bi bi-check-circle me-2"></i>正在测试JavaScript模块...
                </div>
            </div>
            <button class="btn btn-primary" onclick="testJavaScript()">运行JavaScript测试</button>
        </div>

        <!-- 测试结果汇总 -->
        <div class="test-section">
            <h3>测试结果汇总</h3>
            <div id="test-summary">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    重构测试页面已加载，请检查各组件的显示效果和功能。
                </div>
            </div>
        </div>
    </div>

    <script src="web/static/libs/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="web/static/js/main.js"></script>
    <script>
        function testJavaScript() {
            const resultsDiv = document.getElementById('js-test-results');
            const summaryDiv = document.getElementById('test-summary');
            let results = [];

            // 测试全局配置
            try {
                if (typeof CONFIG !== 'undefined' && CONFIG.API_BASE_URL) {
                    results.push('<div class="test-result test-pass"><i class="bi bi-check-circle me-2"></i>CONFIG对象加载成功</div>');
                } else {
                    results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>CONFIG对象加载失败</div>');
                }
            } catch (e) {
                results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>CONFIG测试异常: ' + e.message + '</div>');
            }

            // 测试AppState类
            try {
                if (typeof appState !== 'undefined') {
                    results.push('<div class="test-result test-pass"><i class="bi bi-check-circle me-2"></i>AppState实例创建成功</div>');
                } else {
                    results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>AppState实例创建失败</div>');
                }
            } catch (e) {
                results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>AppState测试异常: ' + e.message + '</div>');
            }

            // 测试API客户端
            try {
                if (typeof api !== 'undefined') {
                    results.push('<div class="test-result test-pass"><i class="bi bi-check-circle me-2"></i>API客户端创建成功</div>');
                } else {
                    results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>API客户端创建失败</div>');
                }
            } catch (e) {
                results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>API客户端测试异常: ' + e.message + '</div>');
            }

            // 测试UI工具类
            try {
                if (typeof UIUtils !== 'undefined') {
                    results.push('<div class="test-result test-pass"><i class="bi bi-check-circle me-2"></i>UIUtils类加载成功</div>');
                    
                    // 测试showAlert方法
                    UIUtils.showAlert('这是一个测试消息', 'success', 3000);
                    results.push('<div class="test-result test-pass"><i class="bi bi-check-circle me-2"></i>UIUtils.showAlert方法测试成功</div>');
                } else {
                    results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>UIUtils类加载失败</div>');
                }
            } catch (e) {
                results.push('<div class="test-result test-fail"><i class="bi bi-x-circle me-2"></i>UIUtils测试异常: ' + e.message + '</div>');
            }

            // 更新结果显示
            resultsDiv.innerHTML = results.join('');

            // 更新汇总
            const passCount = results.filter(r => r.includes('test-pass')).length;
            const failCount = results.filter(r => r.includes('test-fail')).length;
            const total = passCount + failCount;

            let summaryClass = 'alert-success';
            let summaryIcon = 'check-circle';
            let summaryText = `所有测试通过！(${passCount}/${total})`;

            if (failCount > 0) {
                summaryClass = 'alert-warning';
                summaryIcon = 'exclamation-triangle';
                summaryText = `部分测试失败：通过 ${passCount}/${total}，失败 ${failCount}`;
            }

            summaryDiv.innerHTML = `
                <div class="alert ${summaryClass}">
                    <i class="bi bi-${summaryIcon} me-2"></i>
                    ${summaryText}
                </div>
            `;
        }

        // 页面加载完成后自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(testJavaScript, 1000);
        });
    </script>
</body>
</html>
